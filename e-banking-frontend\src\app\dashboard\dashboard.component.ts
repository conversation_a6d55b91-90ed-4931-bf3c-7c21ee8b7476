import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTabsModule } from '@angular/material/tabs';
import { BaseChartDirective } from 'ng2-charts';
import {
  Chart,
  ChartConfiguration,
  ChartData,
  ChartType,
  registerables,
} from 'chart.js';
import { Subject, forkJoin, takeUntil } from 'rxjs';
import { CustomerService } from '../services/customer.service';
import { AccountService } from '../services/account.service';
import { AuthService } from '../services/auth.service';
import { Customer } from '../models/customer';
import { BankAccount } from '../models/account';

Chart.register(...registerables);

interface DashboardStats {
  totalCustomers: number;
  totalAccounts: number;
  totalBalance: number;
  totalTransactions: number;
  savingsAccounts: number;
  currentAccounts: number;
  averageBalance: number;
  recentTransactions: number;
}

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatTabsModule,
    BaseChartDirective,
  ],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css'],
})
export class DashboardComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  isLoading = true;
  stats: DashboardStats = {
    totalCustomers: 0,
    totalAccounts: 0,
    totalBalance: 0,
    totalTransactions: 0,
    savingsAccounts: 0,
    currentAccounts: 0,
    averageBalance: 0,
    recentTransactions: 0,
  };

  customers: Customer[] = [];
  accounts: BankAccount[] = [];

  // User property for profile dropdown (mock data)
  user = {
    name: 'John Doe',
    email: '<EMAIL>',
    avatarUrl: '', // or provide a default avatar path
  };

  // Chart configurations
  accountTypeChartData: ChartData<'doughnut'> = {
    labels: ['Savings Accounts', 'Current Accounts'],
    datasets: [
      {
        data: [0, 0],
        backgroundColor: ['#004D98', '#DC143C'],
        borderColor: ['#FFFFFF', '#FFFFFF'],
        borderWidth: 3,
      },
    ],
  };

  accountTypeChartOptions: ChartConfiguration['options'] = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          padding: 20,
          font: {
            size: 14,
            weight: 'bold',
          },
        },
      },
      title: {
        display: true,
        text: 'Account Distribution',
        font: {
          size: 18,
          weight: 'bold',
        },
        color: '#004D98',
      },
    },
  };

  balanceChartData: ChartData<'bar'> = {
    labels: [],
    datasets: [
      {
        label: 'Account Balance',
        data: [],
        backgroundColor: 'rgba(0, 77, 152, 0.8)',
        borderColor: '#004D98',
        borderWidth: 2,
        borderRadius: 8,
      },
    ],
  };

  balanceChartOptions: ChartConfiguration['options'] = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'Top Account Balances',
        font: {
          size: 18,
          weight: 'bold',
        },
        color: '#004D98',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function (value) {
            return '$' + value.toLocaleString();
          },
        },
      },
    },
  };

  monthlyStatsData: ChartData<'line'> = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'New Customers',
        data: [12, 19, 15, 25, 22, 30],
        borderColor: '#004D98',
        backgroundColor: 'rgba(0, 77, 152, 0.1)',
        tension: 0.4,
        fill: true,
      },
      {
        label: 'New Accounts',
        data: [8, 15, 12, 20, 18, 25],
        borderColor: '#DC143C',
        backgroundColor: 'rgba(220, 20, 60, 0.1)',
        tension: 0.4,
        fill: true,
      },
    ],
  };

  monthlyStatsOptions: ChartConfiguration['options'] = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          padding: 20,
          font: {
            size: 14,
            weight: 'bold',
          },
        },
      },
      title: {
        display: true,
        text: 'Monthly Growth Trends',
        font: {
          size: 18,
          weight: 'bold',
        },
        color: '#004D98',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  constructor(
    private customerService: CustomerService,
    private accountService: AccountService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.loadDashboardData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadDashboardData(): void {
    this.isLoading = true;

    forkJoin({
      customers: this.customerService.getCustomers(),
      accounts: this.accountService.getAllAccounts(),
    })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (data) => {
          this.customers = data.customers;
          this.accounts = data.accounts;
          this.calculateStats();
          this.updateCharts();
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading dashboard data:', error);
          this.isLoading = false;
        },
      });
  }

  private calculateStats(): void {
    this.stats.totalCustomers = this.customers.length;
    this.stats.totalAccounts = this.accounts.length;
    this.stats.totalBalance = this.accounts.reduce(
      (sum, account) => sum + account.balance,
      0
    );
    this.stats.savingsAccounts = this.accounts.filter(
      (acc) => acc.type === 'SavingAccount'
    ).length;
    this.stats.currentAccounts = this.accounts.filter(
      (acc) => acc.type === 'CurrentAccount'
    ).length;
    this.stats.averageBalance =
      this.stats.totalAccounts > 0
        ? this.stats.totalBalance / this.stats.totalAccounts
        : 0;

    // Mock data for transactions (since we don't have a transaction service yet)
    this.stats.totalTransactions = Math.floor(Math.random() * 1000) + 500;
    this.stats.recentTransactions = Math.floor(Math.random() * 50) + 20;
  }

  private updateCharts(): void {
    // Update account type chart
    this.accountTypeChartData.datasets[0].data = [
      this.stats.savingsAccounts,
      this.stats.currentAccounts,
    ];

    // Update balance chart with top 10 accounts
    const topAccounts = this.accounts
      .sort((a, b) => b.balance - a.balance)
      .slice(0, 10);

    this.balanceChartData.labels = topAccounts.map(
      (acc) => `${acc.id.substring(0, 8)}...`
    );
    this.balanceChartData.datasets[0].data = topAccounts.map(
      (acc) => acc.balance
    );
  }

  refreshData(): void {
    this.loadDashboardData();
  }
}
