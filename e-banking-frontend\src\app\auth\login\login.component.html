<!--
  Minimalist e-banking login screen using Bootstrap 5 classes only
  - App background: #0f172a
  - Form background: #020817
  - Button: #3b82f6
  - All text in white for contrast
  - Bootstrap Icons for icons
  - Clean, accessible, responsive
-->
<div
  class="min-vh-100 d-flex align-items-center justify-content-center"
  style="background: #0f172a"
>
  <div
    class="w-100"
    style="
      max-width: 400px;
      background: #020817;
      border-radius: 1.5rem;
      box-shadow: 0 4px 24px rgba(37, 99, 235, 0.08);
      padding: 2.5rem 2rem;
    "
  >
    <div class="d-flex flex-column align-items-center mb-4">
      <i class="bi bi-person-circle fs-1 mb-2" style="color: #3b82f6"></i>
      <h2 class="h4 fw-bold" style="color: #fff">Sign In</h2>
      <p class="text-center mb-0" style="color: #cbd5e1">
        Enter your credentials to access your account
      </p>
    </div>
    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" autocomplete="on">
      <!-- Username -->
      <div class="mb-3">
        <label for="username" class="form-label" style="color: #fff"
          >Email or Username</label
        >
        <div class="position-relative">
          <input
            id="username"
            type="text"
            formControlName="username"
            placeholder="Enter your email or username"
            autocomplete="username"
            aria-label="Email or Username"
            required
            class="form-control pe-5"
            style="background: #0f172a; color: #fff; border: 1px solid #334155"
          />
          <i
            class="bi bi-person position-absolute top-50 end-0 translate-middle-y me-3"
            style="color: #3b82f6"
          ></i>
        </div>
        <div
          *ngIf="
            loginForm.get('username')?.invalid &&
            loginForm.get('username')?.touched
          "
          class="small mt-1"
          style="color: #ef4444"
        >
          {{ getErrorMessage("username") }}
        </div>
      </div>
      <!-- Password -->
      <div class="mb-3">
        <label for="password" class="form-label" style="color: #fff"
          >Password</label
        >
        <div class="position-relative">
          <input
            id="password"
            [type]="hidePassword ? 'password' : 'text'"
            formControlName="password"
            placeholder="Enter your password"
            autocomplete="current-password"
            aria-label="Password"
            required
            class="form-control pe-5"
            style="background: #0f172a; color: #fff; border: 1px solid #334155"
          />
          <button
            type="button"
            (click)="hidePassword = !hidePassword"
            [attr.aria-label]="hidePassword ? 'Show password' : 'Hide password'"
            [attr.aria-pressed]="!hidePassword"
            class="btn btn-link position-absolute top-50 end-0 translate-middle-y p-0"
            style="color: #3b82f6; text-decoration: none"
          >
            <i
              class="bi"
              [ngClass]="hidePassword ? 'bi-eye-slash' : 'bi-eye'"
            ></i>
          </button>
        </div>
        <div
          *ngIf="
            loginForm.get('password')?.invalid &&
            loginForm.get('password')?.touched
          "
          class="small mt-1"
          style="color: #ef4444"
        >
          {{ getErrorMessage("password") }}
        </div>
      </div>
      <!-- Submit -->
      <div class="mt-4">
        <button
          type="submit"
          [disabled]="loading"
          class="btn w-100 fw-semibold py-2 rounded-pill d-flex align-items-center justify-content-center"
          style="background: #3b82f6; color: #fff; border: none"
        >
          <span *ngIf="!loading">Log In</span>
          <span *ngIf="loading">Signing In...</span>
        </button>
      </div>
      <!-- Footer -->
      <div class="text-center mt-4">
        <p class="mb-1" style="color: #fff">
          Don't have an account?
          <a
            routerLink="/register"
            style="color: #3b82f6; text-decoration: underline; font-weight: 500"
            >Register</a
          >
        </p>
        <p
          class="small mt-3 d-flex align-items-center justify-content-center gap-1 mb-0"
          style="color: #94a3b8"
        >
          <i
            class="bi bi-shield-lock me-1"
            style="color: #3b82f6"
            aria-hidden="true"
          ></i>
          Your data is secure.
          <a
            href="#"
            style="
              color: #3b82f6;
              text-decoration: underline;
              margin-left: 0.25rem;
            "
            >Privacy Policy</a
          >
        </p>
      </div>
    </form>
  </div>
</div>
