<mat-card>
  <mat-card-header>
    <mat-card-title>
      {{ isEditMode ? "Edit Customer" : "Add New Customer" }}
    </mat-card-title>
  </mat-card-header>

  <mat-card-content>
    <form [formGroup]="customerForm" (ngSubmit)="onSubmit()" #form="ngForm">
      <mat-form-field appearance="fill">
        <mat-label>Name</mat-label>
        <input matInput formControlName="name" required />
        <mat-error *ngIf="customerForm.get('name')?.hasError('required')">
          Name is required
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="fill">
        <mat-label>Email</mat-label>
        <input matInput formControlName="email" type="email" required />
        <mat-error *ngIf="customerForm.get('email')?.hasError('required')">
          Email is required
        </mat-error>
        <mat-error *ngIf="customerForm.get('email')?.hasError('email')">
          Please enter a valid email
        </mat-error>
      </mat-form-field>

      <div class="d-flex justify-content-end gap-2 mt-4">
        <a class="btn btn-secondary" routerLink="/customers">
          <i class="bi bi-x-circle me-1"></i> Cancel
        </a>
        <button
          class="btn btn-primary"
          type="submit"
          [disabled]="customerForm.invalid"
        >
          <i class="bi bi-check-circle me-1"></i>
          {{ isEditMode ? "Update" : "Save" }}
        </button>
        <!-- <button
          class="btn btn-info text-white"
          type="button"
          (click)="debugForm()"
        >
          <i class="bi bi-bug me-1"></i> Debug
        </button> -->
      </div>

      <div *ngIf="formErrors" class="form-errors">
        <pre>{{ formErrors | json }}</pre>
      </div>
    </form>
  </mat-card-content>
</mat-card>
