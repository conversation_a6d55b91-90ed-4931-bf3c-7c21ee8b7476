<div class="customer-form-container">
  <div class="customer-form-card">
    <div class="form-header">
      <h1 class="form-title">
        <i class="bi bi-person-plus form-icon"></i>
        {{ isEditMode ? "Edit Customer" : "Add New Customer" }}
      </h1>
      <p class="form-subtitle">
        {{
          isEditMode
            ? "Update customer information"
            : "Create a new customer account"
        }}
      </p>
    </div>

    <div class="form-content">
      <form [formGroup]="customerForm" (ngSubmit)="onSubmit()">
        <mat-form-field appearance="outline" class="form-field">
          <mat-label>Full Name</mat-label>
          <input
            matInput
            formControlName="name"
            placeholder="Enter customer's full name"
            required
          />
          <i class="bi bi-person" matSuffix></i>
          <mat-error *ngIf="customerForm.get('name')?.hasError('required')">
            Name is required
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="form-field">
          <mat-label>Email Address</mat-label>
          <input
            matInput
            formControlName="email"
            type="email"
            placeholder="Enter customer's email address"
            required
          />
          <i class="bi bi-envelope" matSuffix></i>
          <mat-error *ngIf="customerForm.get('email')?.hasError('required')">
            Email is required
          </mat-error>
          <mat-error *ngIf="customerForm.get('email')?.hasError('email')">
            Please enter a valid email address
          </mat-error>
        </mat-form-field>

        <div class="form-actions">
          <button
            type="button"
            class="btn btn-secondary"
            routerLink="/customers"
          >
            <i class="bi bi-x-circle me-1"></i> Cancel
          </button>
          <button
            type="submit"
            class="btn btn-primary"
            [disabled]="customerForm.invalid"
          >
            <i class="bi bi-check-circle me-1"></i>
            {{ isEditMode ? "Update Customer" : "Save Customer" }}
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
