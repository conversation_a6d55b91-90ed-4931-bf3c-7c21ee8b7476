/*
  Minimalist, professional e-banking register screen style update:
  - Neutral palette: whites, grays, blue accent
  - High contrast, large touch targets, clean layout
  - Remove gradients, background distractions
  - Consistent font, spacing, and button style
*/
.register-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  padding: 0;
}

.register-wrapper {
  width: 100%;
  max-width: 400px;
}

.register-card {
  padding: 2.5rem 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(37, 99, 235, 0.08);
  background: #fff;
  border: 1px solid #e5e7eb;
}

.register-title {
  font-family: "Roboto", "Open Sans", Arial, sans-serif;
  font-size: 2rem;
  font-weight: 600;
  color: #2563eb;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.register-icon {
  font-size: 2.2rem;
  color: #2563eb;
}

mat-card-subtitle {
  color: #6b7280;
  font-size: 1rem;
  margin-bottom: 1.5rem;
}

.register-form {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.full-width {
  width: 100%;
}

mat-form-field {
  font-size: 1rem;
}

mat-label {
  color: #374151;
  font-size: 1rem;
}

input[matInput] {
  font-size: 1rem;
  font-family: "Roboto", "Open Sans", Arial, sans-serif;
  color: #374151;
  background: #fff;
}

mat-error {
  color: #ef4444;
  font-size: 0.95rem;
}

.register-actions {
  margin-top: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.register-button {
  background: #2563eb;
  color: #fff;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 8px;
  min-height: 48px;
  letter-spacing: 0.02em;
  box-shadow: none;
  transition: background 0.2s;
}
.register-button:disabled {
  background: #93c5fd;
  color: #fff;
}

.register-footer {
  margin-top: 1.5rem;
  text-align: center;
  color: #6b7280;
  font-size: 1rem;
}

.login-link {
  color: #2563eb;
  text-decoration: none;
  font-weight: 500;
  margin-left: 0.25rem;
}
.login-link:hover {
  text-decoration: underline;
}

/* Accessibility: high-contrast input borders */
mat-form-field.mat-form-field-appearance-outline .mat-form-field-outline {
  color: #2563eb;
}

/* Hide spinner background */
.register-spinner .mat-progress-spinner circle {
  stroke: #fff;
}

/* Hide all background/gradient distractions */
.register-container::before,
.register-container::after {
  display: none !important;
}
