/* Simplified Form Styling - Consistent with Login/Register */

/* Form Container Base */
.form-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #004D98 0%, #DC143C 50%, #004D98 100%);
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.form-container::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 191, 0, 0.1) 0%, transparent 70%);
  animation: rotate 20s linear infinite;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Form Wrapper */
.form-wrapper {
  width: 100%;
  max-width: 600px;
  position: relative;
  z-index: 1;
}

/* Form Card */
.form-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 25px 70px rgba(0, 77, 152, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  animation: slideInUp 0.8s ease-out;
}

.form-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(90deg, #004D98 0%, #FFBF00 50%, #DC143C 100%);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Form Header */
.form-header {
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
  z-index: 2;
}

.form-title {
  font-size: 2rem;
  font-weight: 700;
  color: #004D98;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.form-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, #004D98, #FFBF00, #DC143C);
  border-radius: 2px;
}

.form-subtitle {
  color: #666;
  font-size: 1.1rem;
  margin-top: 1rem;
}

.form-icon {
  font-size: 2.5rem;
  color: #004D98;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* Form Content */
.form-content {
  position: relative;
  z-index: 2;
}

.form-row {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.form-row .form-field {
  flex: 1;
}

.form-field {
  margin-bottom: 1.5rem;
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  position: relative;
  z-index: 2;
}

/* Form Footer */
.form-footer {
  text-align: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(0, 77, 152, 0.1);
  position: relative;
  z-index: 2;
}

.form-link {
  color: #004D98;
  text-decoration: none;
  font-weight: 600;
  position: relative;
  transition: all 0.3s ease;
}

.form-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #004D98, #DC143C);
  transition: width 0.3s ease;
}

.form-link:hover {
  color: #DC143C;
  text-decoration: none;
}

.form-link:hover::after {
  width: 100%;
}

/* Simplified Card Styling for Non-Auth Forms */
.simple-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 77, 152, 0.15);
  border: none;
  margin: 2rem auto;
  max-width: 800px;
  position: relative;
  overflow: hidden;
}

.simple-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #004D98 0%, #FFBF00 50%, #DC143C 100%);
}

.simple-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(0, 77, 152, 0.1);
}

.simple-card-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #004D98;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.simple-card-content {
  position: relative;
}

/* Loading States */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  gap: 1rem;
}

.loading-text {
  color: #004D98;
  font-size: 1.1rem;
  font-weight: 500;
}

/* Error States */
.error-container {
  background: rgba(220, 20, 60, 0.05);
  border: 1px solid rgba(220, 20, 60, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
  margin: 1rem 0;
  color: #DC143C;
}

.error-title {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

/* Success States */
.success-container {
  background: rgba(255, 191, 0, 0.05);
  border: 1px solid rgba(255, 191, 0, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
  margin: 1rem 0;
  color: #FF8F00;
}

/* Responsive Design */
@media (max-width: 768px) {
  .form-container {
    padding: 1rem;
  }
  
  .form-card {
    padding: 2rem;
  }
  
  .form-title {
    font-size: 1.8rem;
  }
  
  .form-row {
    flex-direction: column;
    gap: 0;
  }
  
  .form-actions {
    flex-direction: column-reverse;
    gap: 0.5rem;
  }
  
  .form-actions button {
    width: 100%;
  }
  
  .simple-card {
    margin: 1rem;
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .form-card {
    padding: 1.5rem;
  }
  
  .form-title {
    font-size: 1.5rem;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .form-icon {
    font-size: 2rem;
  }
}
