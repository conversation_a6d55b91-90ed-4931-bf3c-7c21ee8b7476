{"name": "amqplib", "homepage": "http://squaremo.github.io/amqp.node/", "main": "./channel_api.js", "version": "0.5.2", "description": "An AMQP 0-9-1 (e.g., RabbitMQ) library and client.", "repository": {"type": "git", "url": "https://github.com/squaremo/amqp.node.git"}, "engines": {"node": ">=0.8 <=9"}, "dependencies": {"bitsyntax": "~0.0.4", "bluebird": "^3.4.6", "buffer-more-ints": "0.0.2", "readable-stream": "1.x >=1.1.9", "safe-buffer": "^5.0.1"}, "devDependencies": {"mocha": "~1", "claire": "0.4.1", "uglify-js": "2.4.x", "istanbul": "0.1.x"}, "scripts": {"test": "make test", "prepublish": "make"}, "keywords": ["AMQP", "AMQP 0-9-1", "RabbitMQ"], "author": "<PERSON> <<EMAIL>>", "license": "MIT"}