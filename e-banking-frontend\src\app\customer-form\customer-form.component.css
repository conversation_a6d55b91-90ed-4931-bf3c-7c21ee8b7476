/* Customer Form - Simplified <PERSON><PERSON><PERSON><PERSON> Styling */
.customer-form-container {
  background: linear-gradient(135deg, #e3f2fd 0%, #ffffff 50%, #ffebee 100%);
  min-height: 100vh;
  padding: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.customer-form-card {
  background: white;
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 20px 60px rgba(0, 77, 152, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  animation: slideInUp 0.8s ease-out;
  max-width: 600px;
  width: 100%;
}

.customer-form-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(90deg, #004d98 0%, #ffbf00 50%, #dc143c 100%);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-header {
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
  z-index: 2;
}

.form-title {
  font-size: 2rem;
  font-weight: 700;
  color: #004d98;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.form-title::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, #004d98, #ffbf00, #dc143c);
  border-radius: 2px;
}

.form-icon {
  font-size: 2.5rem;
  color: #004d98;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.form-subtitle {
  color: #666;
  font-size: 1.1rem;
  margin-top: 1rem;
}

.form-content {
  position: relative;
  z-index: 2;
}

.form-field {
  margin-bottom: 1.5rem;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  position: relative;
  z-index: 2;
}

/* Responsive Design */
@media (max-width: 768px) {
  .customer-form-container {
    padding: 1rem;
  }

  .customer-form-card {
    padding: 2rem;
  }

  .form-title {
    font-size: 1.8rem;
  }

  .form-actions {
    flex-direction: column-reverse;
    gap: 0.5rem;
  }

  .form-actions button {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .customer-form-card {
    padding: 1.5rem;
  }

  .form-title {
    font-size: 1.5rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .form-icon {
    font-size: 2rem;
  }
}
