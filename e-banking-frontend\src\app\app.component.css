/* Navbar styling */
.navbar {
  padding: 0.5rem 1rem;
}

.navbar-brand {
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 1.25rem;
}

.nav-link {
  display: flex;
  align-items: center;
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
  color: rgba(255, 255, 255, 0.8) !important;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  color: white !important;
}

.nav-link.active {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  color: white !important;
}

.user-menu-trigger {
  color: rgba(255, 255, 255, 0.9) !important;
  border: none !important;
  background: transparent !important;
  padding: 8px 16px !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  font-weight: 500 !important;
  text-transform: none !important;
  letter-spacing: normal !important;
  min-height: auto !important;
  line-height: normal !important;
}

.user-menu-trigger:hover {
  background-color: rgba(255, 191, 0, 0.2) !important;
  color: #ffbf00 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
}

/* User dropdown styles with Blaugrana theme */
::ng-deep .user-dropdown {
  margin-top: 12px;
  border-radius: 12px !important;
  overflow: hidden !important;
  box-shadow: 0 8px 32px rgba(0, 77, 152, 0.3) !important;
}

::ng-deep .user-dropdown .mat-mdc-menu-content {
  padding: 0 !important;
  min-width: 250px !important;
  background: white !important;
  border-radius: 12px !important;
}

.user-info {
  padding: 20px;
  background: linear-gradient(135deg, #004d98 0%, #1e6bb8 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.user-info::before {
  content: "";
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(255, 191, 0, 0.1) 0%,
    transparent 70%
  );
  animation: rotate 10s linear infinite;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.user-name {
  font-weight: 700;
  color: white;
  margin-bottom: 6px;
  font-size: 1.1rem;
  position: relative;
  z-index: 1;
}

.user-email {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  position: relative;
  z-index: 1;
}

::ng-deep .user-dropdown .mat-mdc-menu-item {
  padding: 16px 20px !important;
  min-height: 56px !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
}

::ng-deep .user-dropdown .mat-mdc-menu-item:hover {
  background-color: rgba(0, 77, 152, 0.05) !important;
  color: #004d98 !important;
}

::ng-deep .user-dropdown .mat-mdc-menu-item i {
  color: #004d98 !important;
  font-size: 1.2rem !important;
  transition: all 0.3s ease !important;
}

::ng-deep .user-dropdown .mat-mdc-menu-item:hover i {
  color: #dc143c !important;
  transform: scale(1.1) !important;
}

::ng-deep .user-dropdown .mat-mdc-menu-item span {
  font-weight: 500 !important;
  font-size: 1rem !important;
}

::ng-deep .mat-mdc-menu-panel {
  border-radius: 12px !important;
}

/* Main content styling */
.app-content {
  max-width: 1200px;
  margin: 24px auto;
  padding: 0;
  min-height: calc(100vh - 64px);
  background-color: #f5f5f5;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .app-toolbar {
    padding: 0 12px;

    .branding .app-title {
      display: none;
    }

    .nav-links {
      a span {
        display: none;
      }

      a mat-icon {
        margin-right: 0;
      }
    }
  }

  .app-content {
    padding: 0 12px;
  }
}
