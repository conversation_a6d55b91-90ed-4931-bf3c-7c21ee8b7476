/* Import Angular Material pre-built theme (alternative to SCSS theming) */
/* @import "~@angular/material/prebuilt-themes/indigo-pink.css"; */

/* Import Blaugrana theme and custom styles */
@import "./styles/blaugrana-theme.css";
@import "./styles/buttons.css";
@import "./styles/material-overrides.css";
@import "./styles/simplified-forms.css";

html,
body {
  height: 100%;
  margin: 0;
  font-family: Robot<PERSON>, "Helvetica Neue", sans-serif;
}

.spacer {
  flex: 1 1 auto;
}

mat-card {
  margin: 16px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

mat-card mat-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.details-grid {
  display: grid;
  grid-template-columns: max-content auto;
  gap: 16px;
}

/* Form actions styling moved to buttons.css */

.no-history {
  padding: 16px;
  text-align: center;
  color: rgba(0, 0, 0, 0.6);
}

/* Simplified global card styling */
mat-card {
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 77, 152, 0.15) !important;
  border: none !important;
  overflow: hidden !important;
  position: relative !important;
}

mat-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #004d98 0%, #ffbf00 50%, #dc143c 100%);
  z-index: 1;
}

/* Form labels and text */
.form-label,
.text-dark,
.form-control {
  color: #fff !important;
  background: transparent !important;
}

.form-control {
  background: #0f172a !important;
  border: 1px solid #334155 !important;
  color: #fff !important;
}
.form-control:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25) !important;
}

/* Button primary */
.btn-primary,
.btn-primary:disabled {
  background-color: #3b82f6 !important;
  border-color: #3b82f6 !important;
  color: #fff !important;
}
.btn-primary:hover,
.btn-primary:active,
.btn-primary:focus {
  background-color: #2563eb !important;
  border-color: #2563eb !important;
}

/* Secondary text */
.text-secondary,
.text-muted {
  color: #94a3b8 !important;
}

/* Icon color for contrast */
.bi,
.bi-person,
.bi-person-circle,
.bi-envelope,
.bi-eye,
.bi-eye-slash,
.bi-shield-lock {
  color: #3b82f6 !important;
}

/* Links */
a,
.text-primary {
  color: #3b82f6 !important;
}
a:hover,
.text-primary:hover {
  color: #2563eb !important;
}

/* Error text */
.text-danger {
  color: #ef4444 !important;
}

/* For all card-like containers */
.w-100[style*="max-width"] {
  border-radius: 1.5rem !important;
  box-shadow: 0 4px 24px rgba(37, 99, 235, 0.08) !important;
}
