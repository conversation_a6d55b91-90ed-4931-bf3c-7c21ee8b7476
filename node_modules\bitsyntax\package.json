{"author": {"name": "<PERSON>", "email": "<<EMAIL>>"}, "name": "bitsyntax", "description": "Pattern-matching on byte buffers", "version": "0.0.4", "repository": {"type": "git", "url": "git://github.com/squaremo/bitsyntax-js.git"}, "main": "./index", "scripts": {"test": "make test", "prepublish": "make all"}, "engines": {"node": ">=0.6"}, "dependencies": {"buffer-more-ints": "0.0.2"}, "devDependencies": {"pegjs": "0.7.x", "mocha": "1.x"}}